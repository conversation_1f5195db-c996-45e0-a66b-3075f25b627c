<script setup>
  import { ref, computed } from 'vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import Price from '@/components/price/price.vue';
  const props = defineProps({
    goodsObj: {
      type: Object,
      default: () => {},
    },
    showSelectIcon: {
      // 是否显示多选图标
      type: Boolean,
      default: false,
    },
    showCountIcon: {
      // 是否显示数量
      type: Boolean,
      default: false,
    },
    isSelect: {
      // 是否已选中状态
      type: Boolean,
      default: false,
    },
    stopPropagation: {
      type: Boolean,
      default: true,
    },
    border: {
      type: Boolean,
      default: true,
    },
    countMax: {
      type: Number,
      default: 99,
    },
    tagText: { // 标签显示的内容
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['on-click', 'on-cancel', 'count-change']);
  const count = ref(props.goodsObj.quantity || props.goodsObj.count || 1);

  const handleNumberChange = (value) => {
    count.value = value;
    emit('count-change', {
      componentCode: props.goodsObj.componentCode,
      count: value,
    });
  };
  const onClick = (e) => {
    if (props.stopPropagation) {
      e.stopPropagation();
    }
    if (props.showSelectIcon && props.isSelect) {
      emit('on-cancel', props.goodsObj);
      return;
    }

    const data = {
      ...props.goodsObj,
    };

    if (props.showCountIcon) {
      data.count = count.value;
    }

    emit('on-click', data);
  };

  const clickCountDiv = (e) => {
    e.stopPropagation();
    if (props.isSelect) return;

    const data = {
      ...props.goodsObj,
    };

    data.count = count.value;
    emit('on-click', data);
  };

  const size = computed(() => {
    const { widthSize, depthSize, heightSize } = props.goodsObj;
    return widthSize && depthSize && heightSize ? `${widthSize}*${depthSize}*${heightSize}` : '暂无尺寸信息';
  });
</script>

<template>
  <div :class="['goods-wrap', isSelect ? 'active' : '', border ? 'with-border' : 'no-border']" @click="onClick">
    <div v-if="tagText">
      <slot name="tag">
        <div class="tag-wrap">{{ tagText }}</div>
      </slot>
    </div>
    <div v-if="showSelectIcon">
      <slot name="select">
        <div :class="['select-item', isSelect ? 'selected' : '']"></div>
      </slot>
    </div>
    <div class="top-img">
      <slot name="image">
        <client-img :src="goodsObj.mainImage" :preview="false"></client-img>
      </slot>
    </div>
    <div class="bottom-into">
      <slot name="name">
        <div class="name" v-ellipsis-tooltip>{{ goodsObj?.componentDesc }}</div>
      </slot>
      <slot name="size">
        <div class="size text-hidden">
          {{ size }}
        </div>
      </slot>
      <slot name="price">
        <Price :price="goodsObj.pcPrice" :styleObj="{ color: '#FF2121', size: '12px', smallSize: '10px' }"></Price>
      </slot>
      <div v-if="showCountIcon" @click="clickCountDiv">
        <slot name="count">
          <a-input-number
            v-model:value="count"
            :min="1"
            :max="countMax"
            size="small"
            class="count-input"
            @change="handleNumberChange" />
        </slot>
      </div>
    </div>
  </div>
</template>

<style scoped lang="stylus">
    .goods-wrap
      position relative
      width 100%
      height 100%
      display flex
      flex-direction column
      border-radius 10px
      background #F8F7F8
      padding 8px
      text-align left
      box-sizing border-box
      border 1px solid #F8F7F8

    .with-border
      &:hover
       border 1px solid var(--opn--hover-color)
      &.active
       border 1px solid var(--opn--primary-color)

    .no-border
      &:hover
       border none
      &.active
       border none

    .top-img
      height 104px
      width 100%
      object-fit cover
    .bottom-into
      display flex
      flex-direction column
    .name
      @extends .common-title-text
      font-size: 12px
      line-height: 20px
    .size
      color rgba(0, 0, 0, 0.45)
      font-size 10px
      font-weight 400
      font-style normal
      line-height 20px

  .tag-wrap
    position absolute
    top 4px
    left 4px
    width 64px
    height 22px
    border-radius 6px
    background-color rgba(0, 0, 0, 0.45)
    color #fff
    font-size 10px
    font-weight 400
    line-height 22px
    text-align center
    z-index 1
    box-sizing border-box    

  .select-item
    position absolute
    top 8px
    right 8px
    width 16px
    height 16px
    border-radius 4px
    border 1px solid var(--case-item-border-color)
    background #fff
    box-sizing border-box
    z-index 1

  .selected
    background-color var(--opn--primary-color)
    border none
    background-image url('@/assets/images/selected-icon.png');
    background-size cover

  .count-input
    margin-top 3px
    width 50px
    height 18px

  :deep(.ant-input-number-focused)
     border-color var(--d9-color)

  :deep(input.ant-input-number-input)
     color var(--color-00085)
     font-size 10px
     font-weight 400
     height: 18px
     line-height 18px

  :deep(.ant-input-number:hover)
    border-color: var(--d9-color)
</style>
