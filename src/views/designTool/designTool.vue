<script setup>
  import {useCaseDataStore} from '@/store/modules/caseDataStore'
  import { computed, h, onMounted, onUnmounted, ref } from 'vue';
  import PageLayout from '@/layouts/pageLayout.vue';
  import DesignerHeader from '@/views/designTool/components/designHeader/designerHeader.vue';
  import SidebarSecondaryPanel from '@/views/designTool/components/sidebarSecondaryPanel/sidebarSecondaryPanel.vue';
  import LeftSidebarPanel from '@/views/designTool/components/leftSidebarPanel.vue';
  import AttrSidebarPanel from '@/views/designTool/components/attrSidebarPanel/attrSidebarPanel.vue';
  import SaveCaseDialog from '@/views/designTool/components/saveCase/saveCaseDialog.vue';
  import {
    CREATE_CASE_STATE,
    CREATE_QUOTATION_STATE,
    CREATE_RENDER_WORKING_STATE,
    IMPORT_BASE_MAP,
    SAVE_CASE_STATE,
    SET_INTELLIGENT_SCENE_STATE,
    RECOGNISE_DIALOG_STATE,
    CREATE_WORKING_RESULT_STATE,
    useGlobalModalStore,
  } from '@/store/modules/globalModalStore';
  import CreateCase from '@/views/designTool/components/createCase/createCase.vue';
  import ImportBaseMap from '@/views/designTool/components/importBaseMap/importBaseMap.vue';
  import CreateWorking from '@/views/designTool/components/createWorking/createWorking.vue';
  import Engine from '@/components/engine/engine.vue';
  import DrawHouse from './components/drawHouse/drawHouse.vue';
  import IntelligentScene from '@/views/designTool/components/intelligentScene/intelligentScene.vue';
  import WorkingResult from '@/views/designTool/components/createWorking/workingResult.vue';
  import CreateQuotation from '@/views/designTool/components/createQuotation/createQuotation.vue';

  import { deviceToolsBtn } from '@/views/designTool/components/designToolConfig';
  import RecogniseHouse from '@/views/designTool/components/recogniseHouse/recogniseHouse.vue';
  import ViewControl from '@/views/designTool/components/viewControl/viewControl.vue';
  import ScaleControl from '@/views/designTool/components/viewControl/scaleControl.vue';
  import Icon from '@/components/icon/icon.vue';
  import { useDeviceAttributesStore } from '@/store/modules/deviceAttributes';
  import { usePopover } from '@/components/popover';
  import deviceSet from '@/views/designTool/components/viewControl/deviceSet.vue';
  import houseIcon from '@/assets/images/house-default.png';
  import houseIconActive from '@/assets/images/house-active.png';
  import houseHoverIcon from '@/assets/images/house-hover.png';
  import deviceIcon from '@/assets/images/device-default.png';
  import deviceIconActive from '@/assets/images/device-active.png';
  import deviceHoverIcon from '@/assets/images/device-hover.png';

  import { useUserStore } from '@/store/modules/userStore';
  import { useDesignCase } from '@/hooks/useDesignCase';
  import { _API_getIntelligentCase } from '@/api/intelligentApi';

  import {
    getAnnotationAttrs,
    handleSaveCase,
    createSSEConnection,
    closeSSEConnection,
    handleOpenProposal,
  } from '@/views/designTool/designTools';
  import ProductModel from '@/components/productModel/productModel.vue';
  import { isDev } from '@/utils/isEnv';
  import { isArray } from '@/utils/isType';
  import { useLoading } from '@/hooks/useLoading';
  import { message } from 'ant-design-vue';
  import { _API_saveProposalData } from '@/api/proposalApi';

  let { showLoading, hideLoading } = useLoading();

  const { saveData, getPlanStatusFromEngine, fetchDeviceSystemIds, createProposal } = useDesignCase();
  const userStore = useUserStore();
  const globalModalStore = useGlobalModalStore();
  const createCaseState = globalModalStore[CREATE_CASE_STATE];
  const importBaseMapState = globalModalStore[IMPORT_BASE_MAP];
  const saveCaseState = globalModalStore[SAVE_CASE_STATE];
  const createWorkingRenderState = globalModalStore[CREATE_RENDER_WORKING_STATE];
  const setIntelligentScene = globalModalStore[SET_INTELLIGENT_SCENE_STATE];
  const createWorkingResultState = globalModalStore[CREATE_WORKING_RESULT_STATE];
  const createQuotationState = globalModalStore[CREATE_QUOTATION_STATE];
  const recogniseState = globalModalStore[RECOGNISE_DIALOG_STATE];
  const engineRef = ref(null);
  const pageLoading = ref(true);
  const activeTabIndex = ref(0);
  let popoverInstance = null;
  const selectComponentInfo = ref({
    modelValue: false,
    componentCode: null,
  });

  // 处理EngineRef未加载时的action队列
  const pendingActions = ref([]);
  const isEngineLoaded = ref(false);
  let engineCheckInterval = null;
  const sidebarTabs = [
    {
      key: 'drawHouse',
      label: '户型绘制',
      icon: houseIcon,
      hoverIcon: houseHoverIcon,
      activeIcon: houseIconActive,
    },
    {
      key: 'intelligent',
      label: '智控设备',
      icon: deviceIcon,
      hoverIcon: deviceHoverIcon,
      activeIcon: deviceIconActive,
    },
  ];
  const deviceControlsBtn = ref(deviceToolsBtn());
  const deviceAttributesStore = useDeviceAttributesStore();

  const showAttrSidebar = computed(() => {
    return Object.keys(deviceAttributesStore?.attributeInfo).length;
  });

  /**
   * @description 对设备进行的操作事件
   * @param item
   */
  function handleDeviceAction(item) {
    if (item.action === 'deleteDevice' || item.action === 'copyDevice') {
      handleCaseAction({
        action: item.action,
        payload: deviceAttributesStore?.attributeInfo?.attribute?.uuid,
      });
      return;
    }
    handleCaseAction(item);
  }

  /**
   * @description 方案操作
   * @param item
   */
  async function handleCaseAction(item) {
    if (item.disabled) {
      return;
    }

    // 检查EngineRef是否已加载
    if (!isEngineLoaded.value || !engineRef.value) {
      // 将action添加到待处理队列
      pendingActions.value.push(item);
      // 开始轮询检查EngineRef
      startEnginePolling();
      return;
    }

    // Engine已加载，直接执行action
    await executeActionDirectly(item);
  }

  /**
   * @description 监听属性变化
   */
  deviceAttributesStore.$subscribe(async (_, state) => {
    deviceControlsBtn.value = deviceToolsBtn();
    console.log('监听属性变化', state);
    // todo 只有选择设备是才会显示popover
    if (state?.selectType === 'Device' && Object.keys(state?.attributeInfo).length) {
      let attributeInfo = state.attributeInfo?.attribute;
      let position = state.attributeInfo?.attribute?.screenPosition || [];
      if (!(isArray(position) && position.length) && setIntelligentScene.show) {
        return;
      }
      popoverInstance = usePopover({
        visible: true,
        content: h(deviceSet, {
          onAction: (item) => {
            if (item.action === 'replaceDevice') {
              selectComponentInfo.value = {
                modelValue: true,
                selectedSystemId: attributeInfo.systemId,
                uuid: attributeInfo?.uuid,
                selectedSubModule: {
                  type: 'room',
                  subModuleId: attributeInfo.baseSubModuleId,
                },
                selectComponentCodes: [attributeInfo],
              };
              closePopover();
              return;
            }
            if (item.action === 'annotation') {
              deviceAttributesStore.setSelectType('Annotation');
              deviceAttributesStore.setAttribute({
                selectType: 'Annotation',
                attribute: getAnnotationAttrs(attributeInfo),
              });
              closePopover();
              return;
            }
            handleDeviceAction(item);
            closePopover();
          },
        }),
        x: position[0] + 50,
        y: position[1] + 25,
      });
    } else {
      closePopover();
    }
  });

  /**
   * @description 关闭设备的popover
   */
  function closePopover() {
    if (popoverInstance && popoverInstance.close) {
      popoverInstance.close();
      popoverInstance = null;
    }
  }

  /**
   * @description 选择替换的商品
   * @param selectedArr
   */
  function handleSelectReplaceComponent(selectedArr) {
    let component = selectedArr && selectedArr[0];
    let componentCode = component?.componentCode;
    if (componentCode !== selectComponentInfo.value.selectComponentCode) {
      handleCaseAction({
        action: 'replaceDevice',
        payload: {
          uuid: selectComponentInfo.value.uuid,
          device: {
            ...component,
          },
        },
      });
      handleReplaceClose();
    } else {
      message.error('替换的商品与原商品一致,替换失败');
    }
  }

  /**
   * @description 关闭商品替换的弹窗
   */
  function handleReplaceClose() {
    selectComponentInfo.value = {};
  }

  /**
   * @description 绘制墙体
   * @param item
   */
  function handleDraw(item) {
    if (item.payload) {
      engineRef.value.engineSdkApi(item, item.payload);
    } else {
      engineRef.value.engineSdkApi(item);
    }
  }

  /**
   * @description 设计工具加载成功
   */
  function handleEngineLoaded() {
    console.log('loader');
    hideLoading();
    isEngineLoaded.value = true;

    // 处理待处理的actions
    processPendingActions();
  }

  /**
   * @description 开始轮询检查EngineRef是否加载
   */
  function startEnginePolling() {
    if (engineCheckInterval) {
      return; // 已经在轮询中
    }

    engineCheckInterval = setInterval(() => {
      if (isEngineLoaded.value && engineRef.value) {
        clearInterval(engineCheckInterval);
        engineCheckInterval = null;
        processPendingActions();
      }
    }, 100); // 每100ms检查一次
  }

  /**
   * @description 处理待处理的actions
   */
  async function processPendingActions() {
    if (pendingActions.value.length === 0) {
      return;
    }

    // 确保engine已经加载
    if (!isEngineLoaded.value || !engineRef.value) {
      return;
    }

    const actionsToProcess = [...pendingActions.value];
    pendingActions.value = [];

    for (const action of actionsToProcess) {
      try {
        // 直接执行action逻辑，跳过engine检查
        await executeActionDirectly(action);
      } catch (error) {
        console.error('处理待处理action时出错:', error, action);
      }
    }
  }

  /**
   * @description 直接执行action，跳过engine检查
   * @param item
   */
  async function executeActionDirectly(item) {
    if (item.disabled) {
      return;
    }

    if (item.action === 'replaceDevice') {
      closePopover();
    }

    // 点击保存方案
    if (item.action === 'save') {
      // 用户未保存方案
      const isSave = userStore.saveCaseData && 'code' in userStore.saveCaseData;
      const { drawContent, hasBackgroundImage, hasIntelligentGoods } = await getPlanStatusFromEngine(
        engineRef?.value.engine
      );
      // 用户未进行任何操作
      const isUserNoOperation = !drawContent && !hasBackgroundImage && !hasIntelligentGoods;
      if (isUserNoOperation) {
        message.warning('请先设计方案再保存!');
        return;
      }
      showLoading({
        useCustomIcon: true,
        tip: '',
      });
      // 获取最新渲染图
      let img = await handleSaveCase(engineRef?.value.engine, {
        systemId: 'ALL',
      }); // 保存成功
      if (isSave) {
        // 再次保存
        await saveData(engineRef?.value.engine, { code: userStore.saveCaseData?.code, thumbnail: img });
        hideLoading();
        return;
      } else {
        hideLoading();
        globalModalStore.setStoreState(SAVE_CASE_STATE, {
          show: true,
          props: {
            title: '保存方案',
            caseId: item.caseId || '',
            img: img || '',
          },
        });
        return;
      }
    }
    // 渲染施工图
    if (item.action === 'generateWorkDrawing') {
      globalModalStore.setStoreState(CREATE_RENDER_WORKING_STATE, {
        show: true,
        props: {
          title: '生成施工图',
          activeSystemIds: await fetchDeviceSystemIds(engineRef.value.engine),
        },
      });
      return;
    }
    // 新建设计
    if (item.action === 'newDesign') {
      globalModalStore.setStoreState(CREATE_CASE_STATE, {
        show: true,
        props: {
          title: '新建设计',
          caseId: item.caseId || '',
        },
      });
      return;
    }
    if (item.action === 'intelligentScene') {
      await executeActionDirectly({action: 'getSpaces'});
      if(getStoreVariable().sceneSpaceList?.length === 0) {
        message.warning('请先绘制一个完整的空间');
        return;
      } else {
        globalModalStore.setStoreState(SET_INTELLIGENT_SCENE_STATE, {
          show: true,
          props: {},
        });
        return;
      }
    }
    if (item.action === 'quote') {
      globalModalStore.setStoreState(CREATE_QUOTATION_STATE, {
        props: {
          title: '创建报价单',
          code: userStore.saveCaseData?.code,
        },
      });
      return;
    }

    if (item.action === 'generateProposal') {
      showLoading({
        useCustomIcon: true,
        tip: '电子提案数据生成中...',
      });
      console.log('生成电子提案书', engineRef?.value.engine);
      let code = userStore.saveCaseData.code;
      let version = userStore.saveCaseData.version;
      let data = await createProposal(engineRef?.value.engine);
      console.log('电子提案书path数据', data);
      _API_saveProposalData(code, version, data).then(async (res) => {
        console.log('电子提案数据提交成功', res);
        if (res.code === '0') {
          await handleOpenProposal(code);
          hideLoading();
        } else {
          hideLoading();
        }
      });
      return;
    }
    await engineRef?.value.engineSdkApi(item);
  }
  function getStoreVariable() {
    return useCaseDataStore();
  }
  function handleLoadError() {
    message.error('主画布加载失败');
    pageLoading.value = false;
  }

  function handleChangeTab(index) {
    activeTabIndex.value = index;
    if (index === 1) {
      handleCaseAction({ action: 'cancelDraw' });
    }
  }

  const handleConfirm = async (caseInfo) => {
    console.log('caseInfo', caseInfo, engineRef?.value.engine);
    await saveData(engineRef?.value.engine, {
      name: caseInfo.name,
      thumbnail: caseInfo.thumbnail,
      ...caseInfo.address,
      commName: caseInfo.detailAddress,
    });
    globalModalStore.clearStoreState(SAVE_CASE_STATE);
  };

  onMounted(async () => {
    showLoading({
      useCustomIcon: true,
      tip: '方案数据努力加载中...',
    });
    // 是否编辑方案
    if (userStore.caseCode) {
      console.log('userStore.caseCode', userStore.caseCode);
      // 编辑方案
      const res = await _API_getIntelligentCase({
        code: userStore.caseCode,
      });
      console.log('res', res);
      userStore.setSaveCaseData(res?.data || {});
      userStore.setVersion(res?.data?.version);
      try {
        await handleCaseAction({
          action: 'importPlan',
          payload: JSON.parse(res?.data.toolData || '{}'),
        });
        hideLoading();
      } catch (e) {
        hideLoading();
      }
    }
    if (!isDev()) {
      createSSEConnection();
      window.addEventListener('beforeunload', (event) => {
        event.preventDefault();
        event.returnValue = '';
      });
    }
  });

  onUnmounted(() => {
    closeSSEConnection();
    window.removeEventListener('beforeunload', () => {});

    // 清理轮询定时器
    if (engineCheckInterval) {
      clearInterval(engineCheckInterval);
      engineCheckInterval = null;
    }
  });
</script>

<template>
  <page-layout>
    <template #header>
      <div class="design-tool-header">
        <designer-header :engine="engineRef?.engine" @on-action="handleCaseAction"></designer-header>
      </div>
    </template>
    <template #container>
      <div class="design-tool-container">
        <div class="design-tool-content-left">
          <LeftSidebarPanel :tabs="sidebarTabs" @changeActive="handleChangeTab">
            <template #intelligent>
              <SidebarSecondaryPanel @drag-end="handleCaseAction"></SidebarSecondaryPanel>
            </template>
            <template #drawHouse>
              <DrawHouse @on-action="handleDraw"></DrawHouse>
            </template>
          </LeftSidebarPanel>
        </div>
        <!--        选择商品时的控制按钮    -->
        <div class="design-tool-content-main">
          <div class="device-tools" v-if="activeTabIndex === 1">
            <div
              :class="['tools-item', ite.disabled ? 'disabled' : '']"
              v-for="(ite, _index) in deviceControlsBtn"
              :key="_index"
              @click="handleDeviceAction(ite)">
              <div class="icon">
                <Icon :icon="ite.icon" :size="20"></Icon>
              </div>
              <div class="label">
                {{ ite.label }}
              </div>
            </div>
          </div>
          <!--          底图设置  及显示控制      -->
          <div class="view-control">
            <view-control @onAction="handleCaseAction"></view-control>
          </div>
          <div class="scale-control">
            <scale-control @onAction="handleCaseAction"></scale-control>
          </div>
          <Engine
            :designJson="{}"
            ref="engineRef"
            @canvasLoaded="handleEngineLoaded"
            @load-error="handleLoadError"></Engine>
        </div>
        <div class="design-tool-content-right" v-if="showAttrSidebar">
          <AttrSidebarPanel @on-action="handleCaseAction"></AttrSidebarPanel>
        </div>
      </div>

      <ProductModel
        title="商品替换"
        :model-value="selectComponentInfo.modelValue"
        :select-component-codes="selectComponentInfo.selectComponentCodes"
        :selected-sub-module="selectComponentInfo.selectedSubModule"
        :selected-system-id="selectComponentInfo.selectedSystemId"
        :showSelectIcon="false"
        :show-count-icon="false"
        @select-product="handleSelectReplaceComponent"
        @close="handleReplaceClose"></ProductModel>

      <!--    以下是公共的弹窗 抽屉等组件     -->
      <!--      方案保存弹窗       -->
      <save-case-dialog
        v-if="saveCaseState.show"
        v-bind="saveCaseState.props"
        @confirm="handleConfirm"></save-case-dialog>
      <!--      新建设计      -->
      <create-case v-if="createCaseState.show" v-bind="createCaseState.props"></create-case>
      <!--   导入底图   -->
      <import-base-map v-if="importBaseMapState.show" v-bind="importBaseMapState.props" @on-action="handleCaseAction" />
      <!--      施工图渲染      -->
      <create-working
        v-if="createWorkingRenderState.show"
        v-bind="createWorkingRenderState.props"
        :engine="engineRef?.engine"></create-working>
      <!--      智能场景设置      -->
      <intelligent-scene
        v-if="setIntelligentScene.show"
        v-bind="setIntelligentScene.props"
        @getSpaces="handleCaseAction"
        @getDevices="handleCaseAction"
        @getSelectScenes="handleCaseAction"
        @setSelectScenes="handleCaseAction"
        @addProducts="handleCaseAction"
        @deletedProducts="handleCaseAction"
        @createQuotation="handleCaseAction"></intelligent-scene>
      <working-result v-if="createWorkingResultState.show" v-bind="createWorkingResultState.props"></working-result>
      <!--      创建报价单      -->
      <create-quotation v-if="createQuotationState.show" v-bind="createQuotationState.props"></create-quotation>
      <!--      户型识别弹窗     -->
      <recognise-house
        v-if="recogniseState.show"
        v-bind="recogniseState.props"
        @onAction="handleCaseAction"></recognise-house>
    </template>
  </page-layout>
</template>

<style scoped lang="stylus">
  .design-spin
    width 100%
    height 100%
  .design-tool-header
    width 100%
    height 60px
  .design-tool-container
    height 100%
    display flex
    flex-direction row
  .design-tool-content-left
    position: absolute;
    width: 300px;
    height: calc(100% - 50px);
    top: 50px;
  .design-tool-content-main
    flex: 1;
  .view-control
    position absolute
    bottom 16px
    z-index 10
    left 320px
    display flex
    flex-direction row
    gap 12px
  .scale-control
    position absolute
    z-index 10
    bottom 16px
    right 260px
  .sse-status
    position absolute
    z-index 10
    top 16px
    right 16px
    display flex
    align-items center
    gap 8px
    padding 8px 12px
    background rgba(255, 255, 255, 0.9)
    border-radius 6px
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
    font-size 12px
  .design-tool-content-right
    width 240px
    height calc(100% - 310px)
    overflow-y auto
    position absolute
    top 302px
    right 0
    bottom 8px
    border-radius: 10px
    background #fff
    box-shadow: 0 5.938px 15.835px -7.918px rgba(0, 0, 0, 0.08), 0px 8.907px 28.701px 0px rgba(0, 0, 0, 0.05), 0px 11.876px 47.506px 15.835px rgba(0, 0, 0, 0.03)
  .design-spin
    position absolute
    width 100%
    height 100%
    background rgba(0,0,0,0.4)
    z-index: 23
  .device-tools
    width 224px
    height 50px
    position fixed
    z-index 11;
    top 62px
    left: 50%
    transform  translateX(-50%)
    display flex
    flex-direction  row;
    justify-content center
    align-items center
    background #fff;
    color #333333
    padding: 4px 20px;
    gap: 12px;
    border-radius 10px
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.10)
    .tools-item
      padding 2px 6px
      display flex
      flex-direction column
      align-items center
      flex-shrink 0
      cursor: pointer
      &.disabled
        opacity 0.4;
      .label
        line-height 20px
        @extends .content-small
        color: #000
        font-size 11px
        opacity 0.65
</style>
