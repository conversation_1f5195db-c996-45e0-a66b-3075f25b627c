<script setup>
  import {
    useGlobalModalStore,
    CREATE_RENDER_WORKING_STATE,
    CREATE_WORKING_RESULT_STATE,
  } from '@/store/modules/globalModalStore';
  import { onMounted, ref } from 'vue';
  // import ClientImg from '@/components/clientImg/clientImg.vue';
  import { message } from 'ant-design-vue';
  import { createWorkingForm } from '@/views/designTool/components/createWorking/formList';
  import { useDictStore } from '@/store/modules/dictStore';
  import { SYSTEM_LIST } from '@/config/dictKeys';
  import { useDesignCase } from '@/hooks/useDesignCase';
  import { useCaseDataStore } from '@/store/modules/caseDataStore';

  const caseDataStore = useCaseDataStore();
  const { renderImgs } = useDesignCase();
  const globalModalStore = useGlobalModalStore();
  const dictStore = useDictStore();

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    activeSystemIds: {
      type: Array,
      default: () => [],
    },
    engine: {
      type: Object,
      default: () => null,
    },
  });
  const confirmLoading = ref(false);
  const open = ref(true);
  const createLoading = ref(false);
  const formList = ref([]);
  const formData = ref({});
  const formRef = ref(null);
  /**
   * @description 方案保存确认
   */
  async function handleConfirm() {
    if (!formRef.value) return;
    try {
      await formRef.value.validate();
      console.log('formData.value', formData.value);

      // 清空store内渲染图列表
      caseDataStore.renderingImgList = [];

      if (formData.value?.imgType) {
        const list = formData.value?.imgType.map((item) => ({
          systemId: item,
          systemName: dictStore.systemList.find((i) => i.value === item)?.label,
          type: '1',
          showAnnotations: true,
          width: formList.value[1].props.options.find((i) => i.value === formData.value.renderSize)?.width,
          height: formList.value[1].props.options.find((i) => i.value === formData.value.renderSize)?.height,
        }));
        await renderImgs(props.engine, list);
      }
      globalModalStore.clearStoreState(CREATE_RENDER_WORKING_STATE);
      globalModalStore.setStoreState(CREATE_WORKING_RESULT_STATE, {
        show: true,
      });
    } catch (error) {
      console.log('error', error);
      if (error.errorFields && error.errorFields.length > 0) {
        const firstError = error.errorFields[0]?.errors[0];
        message.warn(firstError);
      }
    }
  }

  /**
   * @description 取消方案保存
   */
  function handleCancel() {
    if (confirmLoading.value) {
      message.warning('正在保存方案，请稍后再试');
      return;
    }
    globalModalStore.clearStoreState(CREATE_RENDER_WORKING_STATE);
    open.value = false;
  }

  function changeEmitEvent(item) {
    console.log(item);
  }

  onMounted(async () => {
    console.log('props', props);
    let { formItems } = await createWorkingForm();
    console.log('formItems', formItems);
    const renderList = await dictStore.getDictStore(SYSTEM_LIST);
    console.log('renderList', renderList);
    // 给所有renderList子项增加disabled属性，通过activeSystemIds匹配value是否存在
    renderList.forEach((item) => {
      item.disabled = !props.activeSystemIds.includes(String(item.value));
    });
    formList.value = formItems;
  });
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="props.title"
    :maskClosable="false"
    :keyboard="false"
    @cancel="handleCancel"
    width="40%"
    :confirmLoading="confirmLoading">
    <div class="render-working-image">
      <div class="create-container">
        <a-spin :spinning="createLoading">
          <a-form ref="formRef" :model="formData" autocomplete="off" name="formPart" label-align="right">
            <a-form-item
              v-for="item in formList"
              :key="item.name"
              :name="item.name"
              :rules="item.rules"
              :label="item.label">
              <component
                :is="item.is"
                v-model:value="formData[item.name]"
                v-bind="item.props"
                @onChange="(value) => changeEmitEvent(item, value)"></component>
            </a-form-item>
          </a-form>
        </a-spin>
      </div>
    </div>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm">确定</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="stylus">
  .save-case-dialog-container
    display flex
    flex-direction row
    width 100%
    height 100%
  .show-image-container
    display flex
    width 100%
    height 100%
    .left
      width 200px
      height 100%
      overflow auto
    .right
      flex: 1
      height 100%
</style>
