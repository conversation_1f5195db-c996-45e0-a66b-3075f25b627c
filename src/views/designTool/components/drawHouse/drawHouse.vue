<script setup>
  import { onMounted, onUnmounted, ref } from 'vue';
  import importHouse from '@/assets/images/import.png';
  import identify from '@/assets/images/identify.png';
  import straightWall from '@/assets/images/straightwall.png';
  import door from '@/assets/images/door.png';
  import window from '@/assets/images/window.png';
  import { IMPORT_BASE_MAP, RECOGNISE_DIALOG_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
  const globalModalStore = useGlobalModalStore();

  const emit = defineEmits(['onAction']);
  const activeId = ref(null);
  const layoutDataList = ref([
    {
      label: '户型创建',
      list: [
        {
          label: '导入底图',
          icon: importHouse,
          action: 'importBaseMap',
          state: IMPORT_BASE_MAP,
          title: '导入底图',
          id: 'importHouse',
        },
        {
          label: '识别户型',
          icon: identify,
          action: 'recognizeHouse',
          state: RECOGNISE_DIALOG_STATE,
          title: '户型识别',
          id: 'recognizeHouse',
        },
      ],
    },
    {
      label: '墙体创建',
      list: [
        {
          label: '直墙',
          icon: straightWall,
          action: 'draw',
          id: 'drawWall1',
          payload: {
            type: 'Wall',
            subType: 1,
          },
        },
      ],
    },
    {
      label: '墙体构件',
      list: [
        {
          label: '平开门',
          icon: door,
          id: 'drawDoor12',
          action: 'draw',
          payload: {
            type: 'Door',
            subType: 12,
          },
        },
        {
          label: '普通窗',
          icon: window,
          action: 'draw',
          id: 'drawWindow20',
          payload: {
            type: 'Window',
            subType: 20,
          },
        },
      ],
    },
  ]);

  /**
   * @description 构件的点击事件
   * @param item
   */
  function handleClick(item) {
    activeId.value = item.id;
    let showDialogActions = ['importBaseMap', 'recognizeHouse'];
    if (showDialogActions.includes(item.action)) {
      emit('onAction', {
        action: 'cancelDraw',
      });
      globalModalStore.setStoreState(item.state, {
        show: true,
        props: {
          title: item.title,
        },
        event: () => {
          activeId.value = null;
        },
      });
      return;
    }
    emit('onAction', item);
  }

  // ESC键监听函数
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      activeId.value = null;
    }
  };

  // 鼠标右键监听函数
  const handleRightClick = (event) => {
    // 检查是否在主画布上右键，只有在主画布上右键才执行取消操作
    const mainCanvas = event.target.closest('#main-canvas') || event.target.id === 'main-canvas';
    if (!mainCanvas) {
      return; // 不在主画布上右键，不执行取消操作
    }
    
    event.preventDefault(); // 阻止默认右键菜单
    
    // 检查是否有选中状态
    if (activeId.value) {
      // 获取当前选中的项目
      const currentItem = layoutDataList.value
        .flatMap(group => group.list)
        .find(item => item.id === activeId.value);
      
      // 通过action判断是否属于墙体创建或墙体构件
      if (currentItem && currentItem.action === 'draw') {
        activeId.value = null;
      }
    }
  };

  onMounted(() => {
    // 添加ESC键监听
    document.addEventListener('keydown', handleKeyDown);
    // 添加鼠标右键监听
    document.addEventListener('contextmenu', handleRightClick);
  });

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('contextmenu', handleRightClick);
  });
</script>

<template>
  <div class="draw-house-wrap">
    <div class="type-item" v-for="(item, index) in layoutDataList" :key="index">
      <div class="label">{{ item.label }}</div>
      <div class="list-content">
        <div
          :class="['list-content-item', activeId === item.id ? 'active' : '']"
          v-for="item in item.list"
          :key="item.label"
          @click="handleClick(item)">
          <img :src="item.icon" alt="" />
          <div class="name">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .draw-house-wrap
    padding 20px 16px
  .type-item
    margin-bottom 24px
  .label
    @extends .title-small
    font-weight 600
    height 20px
    line-height 20px
    margin-bottom 8px
  .type-item:nth-last-of-type(1)
    margin-bottom 0
  .list-content
    display flex
    flex-direction row
    gap: 8px
  .list-content-item
    padding 8px
    border-radius 8px
    background var(--card-hover-gb-color)
    border 1px solid transparent
    &:hover
      background #f0f0f0;
    &.active
      background #FFFBF0;
      border 1px solid #BF8630;
    img
      draggable: false
      -webkit-user-drag: none
      -khtml-user-drag: none
      -moz-user-drag: none
      -o-user-drag: none
      user-drag: none
      width 84px
      height 84px
  .name
    @extends .content-small
    text-align center
</style>
