<template>
  <div class="space-attr-panel">
    <!-- 主标题 -->
    <div class="main-title">{{ configs.title }}</div>
    <a-form ref="formRef" :model="formData" autocomplete="off" size="small" name="formPart" label-align="left">
      <a-form-item :labelCol="{ span: 8 }" :wrapper-col="{ span: 16}" :label="control.label" :colon="control.colon"
        v-for="(control, cIndex) in configs.controls" :key="cIndex" :rules="control.rules" :name="control.name"
        :class="['control-row', control.disabled ? 'disabled' : '']">
        <component :is="control.is" v-bind="control.props" v-model:value="formData[control.name]"
          v-on="control.on || {}" @[control.props.eventName]="(value) => handleChange(control, value)">
          <template v-if="control.unit" #addonAfter>
            <span class="unit-span">{{ control.unit }}</span>
          </template>
        </component>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { debounce } from 'lodash';
import { isArray, isTruly } from '@/utils/isType';
import { initFormConfig, inputFormItem, selectFormItem } from '@/utils/createForm';
// import { useDictStore } from '@/store/modules/dictStore';
import { ColorPicker } from 'vue3-colorpicker';
import 'vue3-colorpicker/style.css';

const fontSizes = [3, 8, 10, 12, 14, 16, 20, 36, 48, 64, 72, 100]

// const dictStore = useDictStore();

const componentMap = {
  fontSize: 'a-select',
  font: 'a-select',
  text: 'a-textarea',
  color: ColorPicker,
};
const props = defineProps({
  attrs: {
    type: Object,
    default: () => ({
      uuid: '',
      attributeList: [],
    }),
  },
});

const emit = defineEmits(['onCallback']);

// 表单引用
const formRef = ref();

// 表单数据
const formData = ref({});
// --- 模拟数据 ---

// 面板配置
const configs = ref({});

const fontsizeOptions = fontSizes.map((item) => ({
  label: item,
  value: item,
}));

const fontOptions = [
  {
    label: 'Arial',
    value: 'Arial',
  },
  {
    label: '黑体',
    value: '黑体',
  },
  {
    label: '宋体',
    value: '宋体',
  },
  {
    label: '楷体',
    value: '楷体',
  },
  {
    label: 'Times New Roman',
    value: 'Times New Roman',
  },
  {
    label: '微软雅黑',
    value: '微软雅黑',
  },
  {
    label: '思源宋体',
    value: '思源宋体',
  },
];

/**
 * @description 修改属性事件（带防抖）
 * @param item
 */
const handleChange = debounce(async (item) => {
  try {
    // 只校验当前修改的表单项
    await formRef.value.validateFields([item.name]);
    if (!isTruly(formData.value[item.name])) {
      return;
    }

    let payload = {
      [item.name]: formData.value[item.name],
    };

    if (item.name === 'fontSize' || item.name === 'font') {
      const options = item.name === 'fontSize' ? fontsizeOptions : fontOptions;
      const selectedOption = options.find((option) => option.value === formData.value[item.name]);
      if (selectedOption) {
        // formData.value.name = selectedOption.label;
        payload[item.name] = selectedOption.value;
      }
    }

    // 校验通过后触发事件
    emit('onCallback', {
      action: 'setAnnotation',
      deviceUuid: props?.attrs.uuid,
      payload: payload,
    });
  } catch (error) {
    console.log('表单校验失败:', error);
    // 校验失败时不触发事件
  }
}, 500); // 300ms 防抖延迟

/**
 * @description 生成formList
 * @param configs
 * @returns {*}
 */
async function createFormList(configs) {
  let obj = {};
  for (const item of configs) {
    let componentItem = {
      is: componentMap[item.key],
      label: item.label,
      name: item.key,
      key: item.key,
      unit: item.unit,
      colon: false,
      disabled: item.disabled,
      labelCol: {
        style: {
          width: '60px',
        },
      },
    };
    if (item.key === 'font') {
      Object.assign(componentItem, {
        ...selectFormItem({
          placeholder: '请选择字体',
          size: 'small',
          options: fontOptions,
          disabled: item.disabled,
          allowClear: false,
          showSearch: false,
          style: {
            width: '100px',
            textAlign: 'start',
          },
          eventName: 'change',
        }),
      });
    }
    if (item.key === 'fontSize') {
      Object.assign(componentItem, {
        ...selectFormItem({
          placeholder: '请选择字号',
          size: 'small',
          options: fontsizeOptions,
          disabled: item.disabled,
          allowClear: false,
          showSearch: false,
          style: {
            width: '100px',
            textAlign: 'start',
          },
          eventName: 'change',
        }),
      });
    }
    if (item.key === 'text') {
      Object.assign(componentItem, {
        ...inputFormItem({
          placeholder: '请输入标注文本',
          maxlength: 100,
          showCount: true,
          size: 'small',
          allowClear: false,
          style: {
            width: '100px',
          },
          eventName: 'blur',
        }),
        rules: [
          {
            required: true,
            message: '请输入标注文本',
            trigger: ['blur', 'change'],
          },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/,
            message: '请输入汉字、数字、字母，不支持字符',
            trigger: ['blur', 'change'],
          },
        ],
      });
    }

    if (item.key === 'color') {
      componentItem.props = {
        pureColor: item.value,
        format: 'hex'
      }; // 配置 emit 事件
      // 或者使用 on 前缀的事件处理
      componentItem.on = {
        pureColorChange: (value) => handlePureColorChange(value, componentItem.name),
      };
    }
    obj[item.key] = componentItem;
    formData.value[item.key] = item.value;
  }
  return obj;
}

/**
 * @description 创建form配置文件
 * @param formList
 */
async function createFormConfigAndFormData(formList) {
  let config = await createFormList(formList);
  let { formItems } = initFormConfig(config);
  configs.value.controls = formItems;
}

// 处理颜色变化
const handlePureColorChange = debounce((value, name) => {
  console.log('颜色变化:', value);
  if (!isTruly(value)) {
    return;
  }

  emit('onCallback', {
    action: 'setAnnotation',
    deviceUuid: props?.attrs.uuid,
    payload: { [name]: value },
  });
});

watch(
  () => props.attrs,
  async (val) => {
    const attributeList = val?.attributeList || [];
    configs.value.title = attributeList[0]?.title;
    const controlsList = attributeList[0]?.list;
    if (isArray(controlsList) && controlsList.length) {
      await createFormConfigAndFormData(controlsList);
    } else {
      configs.value.title = '';
      configs.value.controls = [];
    }
  },
  { immediate: true, deep: true }
);
</script>

<style scoped lang="stylus">
  .space-attr-panel
    width 240px
    display flex
    flex-direction column
    background-color #fff
    padding 20px 16px;

  .main-title
    color: var(--color-00085);
    @extends .title-small
    line-height: 22px; /* 157.143% */
    margin-bottom 12px

   .group-title
      color: var(--color-00085);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;

  .control-row
    margin-bottom 12px

    :deep(.ant-input-number-group-addon)
      padding 5px 8px
      border-color rgba(0, 0, 0, 0.15)
      color rgba(0, 0, 0, 0.25)
      font-size 12px
      font-weight 400
      background-color #fff
    :deep(.ant-input-number-sm)
      border-right none
      color var(--color-00085)
      border-color rgba(0, 0, 0, 0.15)
      line-height: 24px
      background-color #fff
    // border-radius 6px
    :deep(.ant-input-number-input)
      height: 24px
    .unit-span
      color rgba(0, 0, 0, 0.25)
    :deep(.ant-form-item-control) {
      flex-direction column;
      align-items  flex-end !important
    }

  :deep(.ant-input-number-disabled)
    background-color #f5f5f5 !important

  .disabled
    :deep(.ant-input-number-group-addon)
      background-color #f5f5f5 !important
    :deep(.ant-input-number-sm)
      background-color #f5f5f5  !important

  .ant-form-item 
    height 24px
    :deep( .ant-form-item-label) 
      >label 
        @extends .content-small
        color: var(--color-00085)

  :deep(.vc-color-wrap)
    margin-right: 0
    width 48px
    height 24px
    
    box-shadow none
    &.transparent 
      background-image none
    .current-color 
     border-radius 6px  
   :deep(.ant-input-textarea)
     width 208px !important
     margin-top 25px

   :deep(textarea) 
     border-radius 8px 
     width 208px !important
     height 54px
     border 1px solid var(--case-item-border-color)  
     padding 5px 8px
     font-size 12px
    //  color rgba(0, 0, 0, 0.25)
    :deep(.ant-input-status-error:not(.ant-input-disabled))
      border-color: var(--opn-color-required) !important
      
    :deep(.ant-input-textarea-show-count::after)
      color: rgba(0, 0, 0, 0.25)
      font-size 12px
      font-weight 400
      line-height 20px
    :deep(.ant-form-item-explain-error)
      color var(--opn-color-required)
      font-size 12px
      font-weight 400

</style>
