<script setup>
  import { useGlobalModalStore, IMPORT_BASE_MAP } from '@/store/modules/globalModalStore';
  import { ref, toRaw } from 'vue';
  import { message } from '@syn/ant-design4-vue3';
  import CustomTabs from '@/components/customTabs/customTabs.vue';
  import { isFunction } from '@/utils/isType';
  import { useUserStore } from '@/store/modules/userStore';
  import ProductListPage from './productList/ProductListPage.vue';
  import RulerTool from './rulerTool/index.vue';

  const globalModalStore = useGlobalModalStore();
  const importBaseStore = globalModalStore[IMPORT_BASE_MAP];
  const emit = defineEmits(['onAction']);
  const open = ref(true);

  const userStore = useUserStore();

  defineProps({
    title: {
      type: String,
      default: '导入底图',
    },
    caseId: {
      type: String,
      default: '',
    },
  });
  const selectType = ref('fromLibrary');
  const confirmLoading = ref(false);
  const rulerData = ref({ pixelRate: 0, opacity: 1, imageUrl: '' });
  const tabs = ref([
    {
      key: 'fromLibrary',
      title: '从户型库选择',
    },
    {
      key: 'fromLocal',
      title: '手动选择',
    },
  ]);

  function handleOk() {
    const payload = toRaw(rulerData.value);
    if (!payload.imageUrl || payload.pixelRate <= 0) {
      message.warn('请完善底图信息');
      return;
    }
    confirmLoading.value = true;
    emit('onAction', {
      action: 'loadBaseMap',
      payload,
    });
    confirmLoading.value = false;
    handleCancel();
  }

  function handleCancel() {
    // 调用对应modal的Event
    if (isFunction(importBaseStore?.event)) {
      importBaseStore.event();
    }
    globalModalStore.clearStoreState(IMPORT_BASE_MAP);
    rulerData.value = {
      pixelRate: 0,
      opacity: 1,
      imageUrl: '',
    };
  }

  function handleRulerChange(res, address) {
    rulerData.value = {
      opacity: 1,
      ...res,
    };
    userStore.setAddressInfo(address || {});
  }
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="title"
    :maskClosable="false"
    :keyboard="false"
    destroy-on-close
    :width="824"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    wrapClassName="import-base-map-modal"
    @cancel="handleCancel">
    <div class="import-base-map-container">
      <CustomTabs :tabs="tabs" position="center" v-model:activeKey="selectType">
        <template #fromLibrary>
          <ProductListPage @change="handleRulerChange" />
        </template>
        <template #fromLocal>
          <RulerTool @change="handleRulerChange" />
        </template>
      </CustomTabs>
    </div>
  </a-modal>
</template>

<style scoped lang="stylus">
  .import-base-map-container
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;

  .address-selector-container
    margin-bottom: 20px
    display flex
    flex-direction row
    align-items center
    justify-content flex-start

  .content-area
    position: relative;

  .layout-grid
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    height: 300px;
    overflow-y: auto;
    padding: 5px;

    .layout-item
      height: 160px;
      background-color: #f0f2f5;
      border-radius: 4px;
      border 1px solid transparent;
      cursor: pointer;
      transition: border-color 0.3s;
      .info
        padding 0 5px
        @extends .ellipsis
        text-align center

      &.selected
        border-color: #1890ff;
</style>
<style lang="stylus">
  // 全局样式，用于控制 modal 高度
  .import-base-map-modal
    .ant-modal-content
      display flex
      flex-direction column
      height 680px !important
    .ant-modal-body
      display flex
      flex-direction column
      flex 1
</style>
