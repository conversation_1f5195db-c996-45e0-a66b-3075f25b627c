<script setup>
import ClientImg from '@/components/clientImg/clientImg.vue'
import VideoCustomize from '@/components/videoCustomize/videoCustomize.vue'
import {returnImg} from '@/utils/tools'
import {ref, reactive} from 'vue'

let $emit = defineEmits(['addProduct']);

// 表格列定义
const columns = [
  {
    title: '场景名称及状态',
    dataIndex: 'name',
    key: 'name',
    width: '180px'
  },
  {
    title: '所有类型',
    dataIndex: 'requiredTypes',
    key: 'requiredTypes'
  }
];

// 设备列表数据
const props = defineProps({
  devices: Array,
  containerHeight: Number,
});

const openVideoVis = ref(false);
const videoUrl = ref('');

// 用于跟踪每个类型项的展开状态
const expandedTypes = reactive({});

const addProduct = (subModuleId, subModuleName) => {
  const data = {
    type: 'subModule',
    subModuleId: subModuleId,
    subModuleName: subModuleName,
    title: '添加-' + subModuleName,
  }
  $emit('addProduct', data);
}
function formatStatus(status) {
  switch (status) {
    case 0:
      return '无设备';
    case 1:
      return '设备缺失';
    case 2:
      return '设备齐全';
    default:
      return '';
  }
}

// 切换类型项的展开状态
function toggleExpand(recordId, typeIndex) {
  const key = `${recordId}-${typeIndex}`;
  expandedTypes[key] = !expandedTypes[key];
}

// 检查类型项是否展开
function isExpanded(recordId, typeIndex) {
  const key = `${recordId}-${typeIndex}`;
  return !!expandedTypes[key];
}
function openVideo(url) {
  videoUrl.value = url;
  openVideoVis.value = true
}

</script>

<template>
  <div class="scene-container">
    <div class="scene-header">
      <div class="scene-padding">
        <div class="header-pro"></div>
        <div class="header-title">场景</div>
      </div>
    </div>
    <a-table
      v-if="props.devices?.length > 0"
      :columns="columns" 
      :data-source="props.devices"
      :pagination="false"
      :bordered="false"
      :scroll="{ y: props.containerHeight ? `${props.containerHeight - 140}px` : 'calc(100vh - 570px)' }"
      class="scene-table"
    >
      <template #bodyCell="{ column, record }">
        <!-- 场景名称列 -->
        <template v-if="column.key === 'name'">
          <div class="scene-name">
            <a-tooltip v-if="record.dynamicVideo" placement="top">
              <template #title>
                <span>{{ record.marketingDesc }}</span>
              </template>
              <div  class="scene-video">
                <video width="148" :src="record.dynamicVideo" class="video-radius"></video>
                <img
                  class="play-icon"
                  src="@/assets/images/scene-video-play.png"
                  @click="openVideo(record.dynamicVideo)"
                  alt="" >
              </div>
            </a-tooltip>
            <a-tooltip v-else-if="record.masterPic" placement="top">
              <template #title>
                <span>{{ record.marketingDesc }}</span>
              </template>
              <div>
                <client-img :width="148" :src="record.masterPic" alt="" class="video-radius"></client-img>
              </div>
            </a-tooltip>
            <div class="market-name">
              {{ record.marketingName }}
            </div>
            <div class="device"
              :class="record.status === 0 ? 'no-device' :  record.status === 1 ?'lack-device' :'complete-device'">{{ formatStatus(record.status) }}</div>
          </div>
        </template>
        <template v-if="column.key === 'requiredTypes'">
          <div class="required-types">
            <div v-for="(item, typeIndex) in record.subModuleList" :key="typeIndex" class="type-item">
              <a-tooltip v-if="Number(item.subModuleName?.length) > 15" placement="top">
                <template #title>
                  <span>{{ item.subModuleName }}</span>
                </template>
                <div class="type-title" :class="{'error-type': item.productList.length === 0}">
                  <div class="sub-title">
                    {{ item.subModuleName }}
                  </div>
                  <div v-if="item.productList?.length > 3" @click="toggleExpand(record.id, typeIndex)" class="arrow-container">
                    <synArrowBottom v-if="!isExpanded(record.id, typeIndex)" class="arrow"/>
                    <synArrowTop v-if="isExpanded(record.id, typeIndex)" class="arrow"/>
                  </div>
                </div>
              </a-tooltip>
              <div v-else class="type-title" :class="{'error-type': item.productList.length === 0}">
                <div class="sub-title">
                  {{ item.subModuleName }}
                </div>
                <div v-if="item.productList?.length > 3" @click="toggleExpand(record.id, typeIndex)" class="arrow-container">
                  <synArrowBottom v-if="!isExpanded(record.id, typeIndex)" class="arrow"/>
                  <synArrowTop v-if="isExpanded(record.id, typeIndex)" class="arrow"/>
                </div>
              </div>
              <div class="product-list" :style="{height: item.productList?.length > 3 && isExpanded(record.id, typeIndex) ? 'auto' : '144px'}">
                <template v-if="item.productList.length > 0">
                  <div v-for="(product, productIndex) in item.productList" :key="productIndex">
                    <div class="pro-item">
                      <img :src="product.mainImage" alt=''>
                      <div class="pro-name">
                        <a-tooltip v-if="Number(product.componentName?.length) > 15" placement="top">
                          <template #title>
                            <span>{{ product.componentName }}</span>
                          </template>
                          {{ product.componentName }}
                        </a-tooltip>
                        <span v-else>{{ product.componentName }}</span>
                      </div>
                    </div>
                  </div>
                </template>
                <div v-if="item.productList.length === 0" class="add-product-link">
                  <div class="no-pro">请添加商品启用场景</div>
                  <div class="add-pro" >
                    <synAdd class="add-icon" @click="addProduct(item.subModuleId, item.subModuleName)"/>
                    <span @click="addProduct(item.subModuleId, item.subModuleName)">添加商品</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>
    </a-table>
    <div class="empty" v-else :style="{ height: props.containerHeight ? `${props.containerHeight - 56}px` : 'calc(100% - 56px)' }">
      <div class="empty-product">
        <div>
          <img src="@/assets/images/empty-product.png" alt="" class="empty-icon">
        </div>
        暂无场景，请先添加商品~
      </div>
    </div>
    <a-modal
      v-model:open="openVideoVis"
      title="视频"
      :centered="true"
      :maskClosable="false"
      :keyboard="false"
      :footer="null"
      width="800px">
      <VideoCustomize
        v-if="openVideoVis"
        class="car-video"
        :controls="true"
        :show-bg="false"
        :showPauseIcon="true"
        :src="returnImg(videoUrl)"></VideoCustomize>
    </a-modal>
  </div>
</template>

<style scoped lang="stylus">
.scene-container {
  flex: 1;
  border-radius: 16px;
  padding: 0;
  position: relative;
  background-color: #fff;
}

.scene-header {
  padding 0;
  color: rgba(0, 0, 0, 0.85);
  font-family: "PingFang SC";
  border-bottom: 1px solid #F0F0F0;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  .scene-padding {
    padding 16px 20px;
  }
}
.header-pro {
  width: 4px;
  height: 20px;
  background: var(--opn-color-primary);
  position: absolute;
  top: 18px;
  left: 0;
}
.header-title {
  color: rgba(0, 0, 0, 0.85);
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.scene-table {
  margin 16px 20px
  border-radius: 10px;
  overflow: hidden;
  
  :deep(.ant-table) {
    border-radius: 10px;
  }
  
  :deep(.ant-table-container) {
    border-radius: 10px;
  }
  
  :deep(.ant-table-thead > tr > th) {
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    padding: 12px !important;
    background: #FAFAFA;
  }
  
  :deep(.ant-table-thead > tr:first-child > th:first-child) {
    border-top-left-radius: 10px;
  }
  
  :deep(.ant-table-thead > tr:first-child > th:last-child) {
    border-top-right-radius: 10px;
  }
  
  :deep(.ant-table-tbody > tr:last-child > td:first-child) {
    border-bottom-left-radius: 10px;
  }
  
  :deep(.ant-table-tbody > tr:last-child > td:last-child) {
    border-bottom-right-radius: 10px;
  }
  
  :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
    vertical-align: top;
    transition: none !important;
  }
  
  /* 禁用所有可能的表格行悬停和选中状态 */
  :deep(.ant-table-tbody > tr),
  :deep(.ant-table-tbody > tr.ant-table-row),
  :deep(.ant-table-tbody > tr:hover),
  :deep(.ant-table-tbody > tr.ant-table-row:hover),
  :deep(.ant-table-tbody > tr.ant-table-row-hover),
  :deep(.ant-table-tbody > tr.ant-table-row-selected),
  :deep(.ant-table-tbody > tr.ant-table-row-selected:hover),
  :deep(.ant-table-tbody > tr.ant-table-placeholder:hover > td),
  :deep(.ant-table-tbody > tr.ant-table-placeholder),
  :deep(.ant-table-tbody > tr.ant-table-placeholder > td) {
    background: transparent !important;
    background-color: transparent !important;
    box-shadow: none !important;
    transition: none !important;
    -webkit-transition: none !important;
    -moz-transition: none !important;
  }
  
  /* 确保表头背景色正确显示 */
  :deep(.ant-table-thead > tr > th) {
    background: #FAFAFA !important;
    transition: none !important;
  }
  
  /* 确保所有单元格在所有状态下都没有任何悬停效果 */
  :deep(.ant-table-tbody > tr > td),
  :deep(.ant-table-tbody > tr:hover > td),
  :deep(.ant-table-tbody > tr.ant-table-row > td),
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td),
  :deep(.ant-table-tbody > tr.ant-table-row-hover > td),
  :deep(.ant-table-tbody > tr.ant-table-row-selected > td),
  :deep(.ant-table-tbody > tr.ant-table-row-selected:hover > td) {
    background: unset !important;
    box-shadow: none !important;
    transition: none !important;
    background-color: transparent !important;
  }
  
  /* 统一设置第一列阴影，确保在所有状态下都保持一致 */
  :deep(.ant-table-tbody > tr > td:first-child),
  :deep(.ant-table-tbody > tr:hover > td:first-child),
  :deep(.ant-table-tbody > tr.ant-table-row > td:first-child),
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td:first-child),
  :deep(.ant-table-tbody > tr.ant-table-row-hover > td:first-child),
  :deep(.ant-table-tbody > tr.ant-table-row-selected > td:first-child),
  :deep(.ant-table-tbody > tr.ant-table-row-selected:hover > td:first-child) {
    box-shadow: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03) !important;
    transition: none !important;
  }

}

.scene-name {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  
  .video-radius {
    border-radius 4px 4px 0 0;
  }
  .scene-video {
    position: relative;
  }
  .play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 28px;
    height: 28px;
    cursor: pointer;
  }
  .market-name {
    width 148px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin 8px 0 4px 0;
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
  }
}

.required-types {
  width 100%;
  overflow-y: hidden;
  overflow-x: auto;
  display flex;
  align-items: flex-start;
  
  &::-webkit-scrollbar {
    display: block !important;
    height: 8px !important;
    width: auto !important;
    scrollbar-width: auto !important;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 4px !important;
    display: block !important;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
  }
}

.type-item {
  min-width 140px;
  width 13vw;
  margin-right: 32px;
  flex-shrink: 0;
}

.type-title {
  background-color: #D9F7BE;
  padding: 4px 8px;
  border-radius: 4px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height 30px;
  color:  #092B00;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  margin-bottom 12px;
  display: flex;
  justify-content space-between;
  &:hover {
    background-color: #B7EB8F;
  }
  .sub-title {
    width: calc(100% - 20px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &.error-type {
    background-color: #FFCCC4;
    &:hover {
      background-color: #FFA59C;
    }
  }
  .arrow-container {
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  .arrow {
    width: 14px;
    height 14px;
    position: relative;
    top -5px;
  }
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  height: 144px;
  overflow: hidden;
  .pro-item {
    display: flex;
    margin-bottom 8px;
    img{
      width: 36px;
      height 36px;
      margin-right 8px;
    }
    .pro-name {
      padding 7px 0;
      color: rgba(0, 0, 0, 0.85);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}



.add-product-link {
  .no-pro {
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .add-icon {
    width 16px;
    height 16px;
  }
  .add-pro {
    margin-top 8px
    color: var(--opn--primary-color);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor pointer;
    display block
    &:hover {
      color: var(--opn--hover-color);
    }
    &:active {
      color: #99641F;
    }
    .add-icon {
      font-size 16px;
      margin-right 2px;
      position relative
      top 3px
    }
  }
}
.device {
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  width: 148px;
  &.no-device {
    color: var(--opn-color-required);
  }
  &.lack-device {
    color: #FA8C16;
  }
  &.complete-device {
    color: #52C41A;
  }
}
.car-video {
  width: 752px;
  height: 500px;
  object-fit: cover;
}
.empty {
  display flex;
  justify-content center;
  align-items: center;
}
.empty-product {
  color: #999;
  text-align: center;
  padding: 30px 0;
  font-size: 14px;
  .empty-icon {
    width 80px
    height 80px
    margin-bottom 12px;
  }
}
</style>
