<script setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    selectedRoom: String,
    subProductList: Array,
    containerHeight: Number,
  })
  let $emit = defineEmits(['addProduct', 'deleteProduct', 'changeQuantity']);
  
  // 商品数据
  const productList = ref([]);
  // 是否有商品
  const hasProducts = ref(true);
  watch(
    () => props.subProductList,
    async (newValue) => {
      hasProducts.value = newValue.length > 0;
      productList.value = newValue;
    },{
      immediate: true,
    }
  );
  // 添加商品
  const addProduct = () => {
    const data = {
      type: 'room',
      title: '添加商品',
    }
    $emit('addProduct', data);
  }
  
  function changeQuantity(component) {
    $emit('changeQuantity', component)
  }
  
  // 删除商品
  const deleteModel = (productType, componentCode) => {
    const product = productList.value.find(p => p.baseSubModuleId === productType);
    if (product) {
      const index = product.goodIds.findIndex(m => m.componentCode === componentCode);
      if (index !== -1) {
        product.goodIds.splice(index, 1);
        if (product.goodIds.length === 0) {
          const productIndex = productList.value.findIndex(p => p.baseSubModuleId === productType);
          if (productIndex !== -1) {
            productList.value.splice(productIndex, 1);
          }
        }
      }
    }
    const allComponentCode = [];
    productList.value?.forEach((product) => {
      product?.goodIds?.forEach((component) => {
        allComponentCode.push(component.componentCode);
      });
    });
    $emit('deleteProduct', { deleteCode: componentCode, allComponentCode: allComponentCode })
    // 检查是否还有商品
    hasProducts.value = productList.value.length > 0;
  }
  const deleteCancel = () => {}
</script>

<template>
  <div class="product-section">
    <div class="product-header">
      <div class="header-pro"></div>
      <div class="header-container">
        <div class="room-title">{{ props.selectedRoom }}-商品列表</div>
        <div class="add-product" @click="addProduct">
          <synAdd class="add-icon"/>
          添加商品
        </div>
      </div>
    </div>
    <div class="product-list" v-if="hasProducts" :style="{ height: props.containerHeight ? `${props.containerHeight - 56}px` : 'calc(100% - 56px)' }">
      <div v-for="(product, index) in productList" :key="index" class="product-category">
        <div class="category-title">{{ product.subModuleName }}</div>
        <div v-for="(component, i) in product.goodIds" :key="i" class="product-item">
          <img :src="component.mainImage" alt="" class="product-image">
          <a-tooltip v-if="component.componentName?.length > 15" placement="top">
            <template #title>
              <span>{{ component.componentName }}</span>
            </template>
            <div class="product-code">
              {{ component.componentName }}
            </div>
          </a-tooltip>
          <div v-else class="product-code">
            {{ component.componentName }}
          </div>
          <div class="quantity-control">
            <a-input-number
              v-model:value="component.quantity"
              @change="changeQuantity(component)"
              class="quantity-input"
              :min="1" :max="99" />
          </div>
          <!-- 删除按钮 -->
          <a-popconfirm
            title="您确定要删除此商品吗?"
            ok-text="确 定"
            cancel-text="取消"
            @confirm="deleteModel(product.baseSubModuleId, component.componentCode)"
            @cancel="deleteCancel"
          >
            <button class="delete-btn">
              <synDelete />
            </button>
          </a-popconfirm>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div class="product-list-empty" v-else :style="{ height: props.containerHeight ? `${props.containerHeight - 56}px` : 'calc(100% - 56px)' }">
      <div class="empty-product">
        <div>
          <img src="@/assets/images/empty-product.png" alt="" class="empty-icon">
        </div>
        暂无商品，请先添加商品~
      </div>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .product-section {
    margin-right: 20px;
    padding: 0;
    height 100%;
  }
  
  .product-header {
    position: relative;
    border-bottom: 1px solid #F0F0F0;
    border-radius 16px 16px 0 0;
    background-color: #fff;
    .header-pro {
      width: 4px;
      height: 20px;
      background: var(--opn-color-primary);
      position: absolute;
      top: 18px;
      left: 0;
    }
    .header-container {
      padding 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0px -1px 0px 0px #F0F0F0 inset;
    }
  }
  
  .room-title {
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
  }
  
  .add-product {
    color: var(--opn--primary-color);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    cursor pointer;
    display flex;
    &:hover {
      color: var(--opn--hover-color);
    }
    &:active {
      color: #99641F;
    }
    .add-icon {
      margin-right 2px;
      position relative
      top 0.5px
    }
  }
  
  .product-list {
    background-color: #fff;
    border-radius 0 0 16px 16px;
    overflow-y scroll
  }
  .product-list-empty {
    background-color: #fff;
    border-radius 0 0 16px 16px;
    display flex
    align-items center
  }
  .empty-product {
    width 445px;
  }
  .product-category {
    padding 20px 20px 3px;
  }
  
  .category-title {
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    margin-bottom 17px;
  }
  
  .product-item {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom 17px;
  }
  
  .product-image {
    width: 36px;
    height: 36px;
    margin-right: 8px;
    border-radius: 6px;
  }
  
  .product-code {
    width 12vw;
    color: rgba(0, 0, 0, 0.85);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .quantity-control {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
  
  .quantity {
    width: 24px;
    text-align: center;
    font-size: 14px;
    color: #333;
  }
  
  .delete-btn {
    width: 24px;
    height: 24px;
    font-size 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    cursor: pointer;
    color: #999;
    transition: all 0.3s;
    padding: 0;
    
    &:hover {
      color: var(--opn--hover-color);
    }
  }
  
  :deep(.ant-popover-open) {
    color: #99641F!important;
  }
  
  .empty-product {
    color: #999;
    text-align: center;
    padding: 30px 0;
    font-size: 14px;
    .empty-icon {
      width 80px
      height 80px
      margin-bottom 12px;
    }
  }
  .quantity-input {
    width 54px;
    height 24px;
  }
  .quantity-input:deep(.ant-input-number-handler-wrap) {
    opacity: 1 !important;
  }
  .quantity-input:deep(.ant-input-number-input) {
    height 24px;
  }

</style>
