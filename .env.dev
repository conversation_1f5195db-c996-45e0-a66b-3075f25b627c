NODE_ENV = production
NODE_DESC = 'dev环境-打包'
VUE_APP_ENV = dev
VUE_APP_PUBLIC_PATH= "/"
VUE_APP_DZPT_URL = "https://syndzpt-test.haier.net"
VUE_APP_DZPT_REQUEST_URL = "https://syndz-test.haier.net"
VUE_APP_URL = "http://zjsj-dev.qd-aliyun-test-internal.haier.net"
VUE_APP_URL_SOCKET = "https://zjsj-dev.haier.net/socket.io"
VUE_APP_ZJHX_URL = "http://dev.qd-aliyun-internal2.haier.net"
VUE_APP_NESTING_URL = "http://zjsj-dev.qd-aliyun-test-internal.haier.net"
VUE_APP_GIO_ACCOUNT_ID = "8c7b5e8bb1e08076"
VUE_APP_GIO_DATASOURCE_ID = "a02cb2a94e7c7595"
VUE_APP_SENTRY = "dev"
VUE_APP_IMA = "https://iam-test.haier.net"
VUE_APP_IMA_ID = "K1a5184c44a45cc72"
VUE_APP_OPERATE_URL = "http://zjsj-dev.qd-aliyun-test-internal.haier.net"
VUE_APP_EXIT_URL = "http://zjsj-dev.qd-aliyun-test-internal.haier.net/"
VUE_APP_PLATFORM = "NESTING_DESIGNER_PLATFORM_VUE3"
VUE_APP_ISM_MANAGER_GOODS_RUL="https://ismtest.haier.net"
VUE_APP_ARMS_ENV="local"
VUE_APP_LOGIN_URL="https://syndzpt-test.haier.net/console/login"
VUE_APP_OSS_ORIGIN="https://haier-tdcd-04.oss-cn-qingdao.aliyuncs.com"
# 掌上导购链接
VUE_APP_SHOPPING_GUIDE_URL="https://ys-zjrs.haier.net/haierActivitys/shopping-guide/index.html"

# 设计工具动态组件
VUE_APP_3D_ENGINE_URL=https://syntest.haier.net/resourceTest/statics/resource/intecontrol/js/intelligent-control-pc.js

#智家接口hots
VUE_APP_ZJ_API_URL=https://zj-yanshou.haier.net

#提案书地址
VUE_APP_SMART_CONTROL_PAGE=https://ys-zjrs.haier.net/haierActivitys/smart-proposal/index.html

#门户地址
VUE_APP_PORTAL_URL=https://syndz-test.haier.net/#/



