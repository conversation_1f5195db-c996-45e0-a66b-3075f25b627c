NODE_ENV = development
VUE_APP_ENV = dev
NODE_DESC = '测试环境-打包'
VUE_APP_PUBLIC_PATH= "/"
VUE_APP_DZPT_URL = "https://syndzpt-test.haier.net"
VUE_APP_DZPT_REQUEST_URL = "https://syndz-test.haier.net"
VUE_APP_URL = "https://syn-design-test.haier.net"
VUE_APP_URL_SOCKET = "https://zjsj-test.haier.net/socket"
VUE_APP_NESTING_URL = "https://zjsj-test.haier.net/nesting-test"
VUE_APP_GIO_ACCOUNT_ID = "8c7b5e8bb1e08076"
VUE_APP_GIO_DATASOURCE_ID = "a02cb2a94e7c7595"
VUE_APP_SENTRY = "test"
VUE_APP_IMA = "https://iam-test.haier.net"
VUE_APP_IMA_ID = "K1a5184c44a45cc72"
VUE_APP_OPERATE_URL = "https://zjsj-test.haier.net"
VUE_APP_EXIT_URL = "https://zjsj-test.haier.net"
VUE_APP_PLATFORM = "NESTING_DESIGNER_PLATFORM_VUE3"
VUE_APP_ISM_MANAGER_GOODS_RUL="https://ismtest.haier.net"
VUE_APP_ARMS_ENV="daily"
VUE_APP_LOGIN_URL="https://syndzpt-test.haier.net/console/login"
VUE_APP_OSS_ORIGIN="https://haier-tdcd-04.oss-cn-qingdao.aliyuncs.com"

# 设计工具动态组件
VUE_APP_3D_ENGINE_URL=https://syntest.haier.net/resourceTest/statics/resource/intecontrol/js/intelligent-control-pc.js

#智家接口hots
VUE_APP_ZJ_API_URL=https://zj-yanshou.haier.net

#提案书地址
VUE_APP_SMART_CONTROL_PAGE=https://ys-zjrs.haier.net/haierActivitys/smart-proposal/index.html

#门户地址
VUE_APP_PORTAL_URL=https://syndz-test.haier.net/#/



